import { StripeClient } from '@/business/clients/stripe-client';
import { centsToDollars } from '@/lib/utils';
import { CacheNone } from '@shopify/hydrogen';
import Stripe from 'stripe';
import { injectable, Lifecycle, scoped } from 'tsyringe';
import { ServiceAdapter } from '../adapters/service';
import { FeatureQuantities, ServiceProduct, SubscriptionLineItem } from '../types';
import { ADCRepo } from './adc';
import { MetafieldRepo } from './metafield';
import { ShopRepo } from './shop';

const GROUP_METADATA_KEY = 'group';
const LIMIT_METADATA_KEY = 'limit';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class ServiceRepo {
  constructor(
    private readonly stripe: StripeClient,
    private readonly adc: ADCRepo,
    private readonly metafieldRepo: MetafieldRepo,
    private readonly shopRepo: ShopRepo,
    private readonly adapter: ServiceAdapter,
  ) {}

  async getServiceProductsFromSource() {
    const products = await this.stripe.products
      .list({ active: true, expand: ['data.default_price'] })
      .autoPagingToArray({ limit: 10000 });

    const adcTemplates = await this.adc.getDealerTemplates(CacheNone());
    const services = await Promise.all(
      products.map(async product => {
        const price = product.default_price as Stripe.Price;

        if (!price) return undefined!;

        const features = await this.stripe.products.listFeatures(product.id).autoPagingToArray({ limit: 100 });

        const addons = this.adapter.stripeFeaturesToAddonQuantities(features, adcTemplates);

        const serviceProduct: ServiceProduct = {
          group: product.metadata[GROUP_METADATA_KEY] || product.name,
          description: product.description || '',
          name: product.name,
          priceId: price.id,
          monthlyRate: centsToDollars(price.unit_amount),
          iconUrl: product.images?.[0] || undefined,
          addons,
          limit: parseInt(product.metadata[LIMIT_METADATA_KEY]) || undefined,
        };

        return serviceProduct;
      }),
    );

    const sorted = services.filter(s => s).sort((a, b) => a.monthlyRate - b.monthlyRate);

    return sorted;
  }

  private _serviceProducts: ServiceProduct[] | undefined;
  async getServiceProducts() {
    this._serviceProducts ??=
      (await this.metafieldRepo.getShopJSONMetafield<ServiceProduct[]>('all-aware', 'service-products')) || [];
    return this._serviceProducts;
  }

  async getServiceProductsByPriceId(): Promise<{ [priceId: string]: ServiceProduct }> {
    const serviceProducts = await this.getServiceProducts();
    const servicesByPriceId = this.adapter.serviceProductsByPriceId(serviceProducts);
    return servicesByPriceId;
  }

  async getServiceProductsByGroup() {
    const serviceProducts = await this.getServiceProducts();
    const serviceGroups = this.adapter.serviceProductsByGroup(serviceProducts);
    return serviceGroups;
  }

  async setServiceProducts(serviceProducts: ServiceProduct[]) {
    const shopId = await this.shopRepo.getShopID();

    this._serviceProducts = serviceProducts;
    await this.metafieldRepo.setMetafields({
      ownerId: shopId,
      namespace: 'all-aware',
      key: 'service-products',
      value: JSON.stringify(serviceProducts),
      type: 'json',
    });
  }

  async calculateLineItemsTotal({ lineItems }: { lineItems: SubscriptionLineItem[] }) {
    const serviceProducts = await this.getServiceProductsByPriceId();

    const total = lineItems.reduce(
      (prev, line) => prev + serviceProducts[line.price]?.monthlyRate * line.quantity || 0,
      0,
    );

    return total;
  }

  async changeLineItemsByFeatures({
    lineItems,
    change,
  }: {
    lineItems: SubscriptionLineItem[];
    change: (features: FeatureQuantities) => FeatureQuantities | Promise<FeatureQuantities>;
  }): Promise<SubscriptionLineItem[]> {
    const currentFeatures = await this.calculateFeatureQuantitiesFromLineItems({ lineItems });
    const newCurrentFeatures = await change(currentFeatures);
    const newLineItems = await this.calculateLineItemsFromFeatureQuantities(newCurrentFeatures);

    return newLineItems;
  }

  async calculateLineItemsFromFeatureQuantities(features: FeatureQuantities): Promise<SubscriptionLineItem[]> {
    const lineItems: SubscriptionLineItem[] = [];
    const serviceGroups = await this.getServiceProductsByGroup();

    for (const group in serviceGroups) {
      const tierList = serviceGroups[group];

      for (const tier of tierList) {
        const minimumNecessaryQuantity = this.adapter.calculateMinimumQuantityToSupportFeatures(features, tier);

        //Exceeds the service product limit
        if (tier.limit && tier.limit < minimumNecessaryQuantity) {
          continue;
        }

        //Has no need for this service product
        if (!minimumNecessaryQuantity) continue;

        lineItems.push({
          price: tier.priceId,
          quantity: minimumNecessaryQuantity,
        });
      }
    }

    return lineItems;
  }

  async calculateFeatureQuantitiesFromLineItems({ lineItems }: { lineItems: SubscriptionLineItem[] }) {
    const serviceProducts = await this.getServiceProducts();

    const currentFeatures = this.adapter.lineItemsToFeatureQuantities(lineItems, serviceProducts);

    return currentFeatures;
  }

  async getTotalSubscriptionAddons(subscription: Stripe.Subscription) {
    const serviceProducts = await this.getServiceProducts();

    const addonQuantities = await this.adapter.subscriptionLinesToAddonQuantities(
      subscription.items.data.map(item => ({ quantity: item.quantity || 0, price: item.price.id })),
      serviceProducts,
    );

    return addonQuantities;
  }
}
