import { z } from 'zod';
import { ADCAddress } from './adc';
import { ADCAddon } from './addons';
import { SystemDeviceHandle } from './device';

export type GIDTypes =
  | 'Shop'
  | 'Customer'
  | 'Company'
  | 'CompanyContact'
  | 'CustomerPaymentMethod'
  | 'Product'
  | 'ProductVariant'
  | 'Order'
  | 'LineItem'
  | 'Metafield';

export type PaginationVariables =
  | {
      last: number;
      startCursor?: string | null;
    }
  | {
      first: number;
      endCursor?: string | null;
    };
export type StripePaginationVariables = {
  limit?: number;
  starting_after?: string;
  ending_before?: string;
};

export type ShopifyProductId = string;
export type AddOnId = number;
export type ShopifyCustomerId = string;
export type ShopifyOrderId = string;
export type ADCCustomerId = number;
export type HomeId = ADCCustomerId;

export const roleSchema = z.enum(['write', 'read']);
export type SystemRole = z.infer<typeof roleSchema>;

export type SystemFeatureIdentifier =
  | {
      method: 'equipment';
      groupId?: number;
      deviceType: number;
    }
  | {
      method: 'hub';
    }
  | {
      method: 'addon';
      addon: ADCAddon;
    };

export type SystemFeatureHandle = SystemDeviceHandle | 'my-circle' | 'safety-button';

export interface SystemFeature {
  name: string;
  imageUrl: string;
  identifier: SystemFeatureIdentifier;
  limit: number;
  isBusinessOnly?: boolean;
}

export type QuantitiesRecord = { [key: string | number]: number };
export type FeatureQuantities = { [handle in SystemFeatureHandle]?: number };
export type AddonQuantities = { [addonId: number]: number };

export interface ServiceProduct {
  priceId: string;
  monthlyRate: number;
  group: string;
  name: string;
  description: string;
  iconUrl?: string;
  addons: AddonQuantities;
  limit?: number;
}

export type SubscriptionLineItem = { price: string; quantity: number };

export type SystemAddress = Omit<ADCAddress, 'countryId'>;

export type CreateSystemADCCustomerInput = (
  | { type: 'business'; company: string }
  | { type: 'personal'; firstName: string; lastName: string }
) & {
  ownerId: string;
  name?: string;
  loginName?: string;
  email: string;
  phoneNumber: string;
  address: SystemAddress;
};
