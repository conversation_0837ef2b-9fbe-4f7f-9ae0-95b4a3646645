import PageLayout from '@/components/PageLayout';
import { Card } from '@/components/ui/card';
import { MigrationFlowSteps } from './components/steps';
import { loader } from './loader';
import { MetaFunction } from 'react-router';
import { generatePageTitle } from '@/lib/utils';

export { loader };

export const meta: MetaFunction = () => [
  {
    title: generatePageTitle('Account Migration'),
  },
];

export default function MigrationFlow() {
  return (
    <PageLayout>
      <div className="size-full min-h-screen bg-muted px-4 py-8 sm:py-16">
        <Card className="mx-auto w-full max-w-lg p-4 sm:p-6">
          <MigrationFlowSteps />
        </Card>
      </div>
    </PageLayout>
  );
}
