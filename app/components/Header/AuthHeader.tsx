import { useEffect, useRef, useState } from 'react';
import { BuilderBlock } from '@builder.io/sdk-react/edge';
import HeaderMenuMobile from './HeaderMenuMobile';
import HeaderDesktopBar from './HeaderDesktopBar';
import { Menu } from 'lucide-react';
import { Button } from '../ui/button';
import { BUSINESS_ROOT_URL, HOME_ROOT_URL } from './config';

export type IMenuItem = { label: string; url: string; hasSubMenu: boolean; blocks: BuilderBlock[] };

export default function AuthHeader() {
  const headerRef = useRef<HTMLDivElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);

  const menuItems = [
    { label: 'Home', url: HOME_ROOT_URL, hasSubMenu: false, blocks: [] },
    { label: 'Business', url: BUSINESS_ROOT_URL, hasSubMenu: false, blocks: [] },
  ];

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <>
      <header
        ref={headerRef}
        className="aa-header fixed top-0 left-0 z-50 flex h-14 w-full flex-col items-center justify-center bg-background shadow-md transition-transform duration-300 ease-in-out md:h-36"
      >
        <HeaderDesktopBar isGlobal />
        <ul className="relative grid h-24 w-full grid-cols-6 px-6 md:px-[5%]">
          <li className="relative col-span-2 flex w-full items-center md:col-span-1 md:hidden">
            <Button variant="ghost" size="icon" onClick={() => setMobileOpen(true)}>
              <Menu />
              <span className="sr-only">Open Mobile Menu</span>
            </Button>
          </li>
          <li className="relative col-span-2 flex w-full items-center md:col-span-1">
            <img
              src="https://cdn.shopify.com/s/files/1/0608/4762/0347/files/h2-personal-logo.svg?v=1742407003"
              alt="logo"
              className="h-8 md:h-12 md:w-fit"
            />
          </li>
        </ul>
      </header>

      {isMounted && (
        <div className="md:hidden">
          <HeaderMenuMobile open={mobileOpen} onOpenChange={setMobileOpen} menuItems={menuItems} />
        </div>
      )}
      <div className="h-14 md:h-36" />
    </>
  );
}
